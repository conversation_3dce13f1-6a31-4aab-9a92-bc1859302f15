@tailwind base;
@tailwind components;
@tailwind utilities;

/* DNA Rain Animation */
@keyframes dna-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes dna-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.dna-particle {
  position: absolute;
  font-size: 2rem;
  animation: dna-fall linear infinite;
  pointer-events: none;
  z-index: 1;
}

.dna-particle:nth-child(1) { left: 10%; animation-duration: 8s; animation-delay: 0s; color: #00C2A8; }
.dna-particle:nth-child(2) { left: 20%; animation-duration: 12s; animation-delay: 2s; color: #FF6B6B; }
.dna-particle:nth-child(3) { left: 30%; animation-duration: 10s; animation-delay: 4s; color: #4ECDC4; }
.dna-particle:nth-child(4) { left: 40%; animation-duration: 14s; animation-delay: 1s; color: #45B7D1; }
.dna-particle:nth-child(5) { left: 50%; animation-duration: 9s; animation-delay: 3s; color: #96CEB4; }
.dna-particle:nth-child(6) { left: 60%; animation-duration: 11s; animation-delay: 5s; color: #FFEAA7; }
.dna-particle:nth-child(7) { left: 70%; animation-duration: 13s; animation-delay: 0.5s; color: #DDA0DD; }
.dna-particle:nth-child(8) { left: 80%; animation-duration: 15s; animation-delay: 2.5s; color: #98D8C8; }
.dna-particle:nth-child(9) { left: 90%; animation-duration: 7s; animation-delay: 4.5s; color: #F7DC6F; }
.dna-particle:nth-child(10) { left: 15%; animation-duration: 16s; animation-delay: 1.5s; color: #BB8FCE; }

.hero-gradient-bg {
  background: linear-gradient(-45deg, #0A0E3F, #141735, #1a1f4a, #0A0E3F);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

.cta-button {
  background: linear-gradient(135deg, #00C2A8, #45B7D1, #4ECDC4);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 194, 168, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 194, 168, 0.4);
  background-position: right center;
}

.floating-dna {
  animation: dna-float 6s ease-in-out infinite;
}

.professional-card {
  backdrop-filter: blur(20px);
  background: rgba(10, 14, 63, 0.8);
  border: 1px solid rgba(0, 194, 168, 0.2);
}

.glow-effect {
  box-shadow: 0 0 50px rgba(0, 194, 168, 0.1);
}

/* Quiz Specific Styles */
.quiz-container {
  background: linear-gradient(135deg, #141735 0%, #1a1f4a 100%);
}

.quiz-section {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom Radio Buttons */
input[type="radio"]:checked {
  background-color: #00C2A8 !important;
  border-color: #00C2A8 !important;
}

input[type="checkbox"]:checked {
  background-color: #00C2A8 !important;
  border-color: #00C2A8 !important;
}

/* Progress Bar Animation */
.progress-bar {
  transition: width 0.5s ease-in-out;
}

/* Quiz Form Styling */
.quiz-input:focus {
  ring-color: #00C2A8;
  border-color: #00C2A8;
}

.quiz-radio:checked {
  background-color: #00C2A8;
  border-color: #00C2A8;
}