{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": null, "names": ["createDebounce", "callback", "ms", "timeoutId", "args", "useMeasure", "debounce", "scroll", "polyfill", "offsetSize", "ResizeObserver", "bounds", "set", "useState", "state", "useRef", "scrollDebounce", "resizeDebounce", "mounted", "useEffect", "forceRefresh", "resizeChange", "scrollChange", "useMemo", "left", "top", "width", "height", "bottom", "right", "x", "y", "size", "areBoundsEqual", "removeListeners", "element", "addListeners", "scrollContainer", "ref", "node", "findScrollContainers", "useOnWindowScroll", "useOnWindowResize", "onWindowResize", "cb", "onScroll", "enabled", "result", "overflow", "overflowX", "overflowY", "prop", "keys", "a", "b", "key"], "mappings": "yEAEA,SAASA,EAAmDC,EAAaC,EAAY,CAC/EC,IAAAA,EAEJ,MAAO,IAAIC,IAA8B,CAChC,OAAA,aAAaD,CAAS,EAC7BA,EAAY,OAAO,WAAW,IAAMF,EAAS,GAAGG,CAAI,EAAGF,CAAE,CAC3D,CACF,CA0CA,SAASG,EACP,CAAE,SAAAC,EAAU,OAAAC,EAAQ,SAAAC,EAAU,WAAAC,CAAW,EAAa,CAAE,SAAU,EAAG,OAAQ,GAAO,WAAY,IACxF,CACR,MAAMC,EACJF,IAAa,OAAO,QAAW,YAAc,KAAqB,CAAA,EAAM,OAAe,gBAEzF,GAAI,CAACE,EACH,MAAM,IAAI,MACR,gJACF,EAGF,KAAM,CAACC,EAAQC,CAAG,EAAIC,EAAuB,CAC3C,KAAM,EACN,IAAK,EACL,MAAO,EACP,OAAQ,EACR,OAAQ,EACR,MAAO,EACP,EAAG,EACH,EAAG,CAAA,CACJ,EAGKC,EAAQC,EAAc,CAC1B,QAAS,KACT,iBAAkB,KAClB,eAAgB,KAChB,WAAYJ,EACZ,mBAAoB,IAAA,CACrB,EAGKK,EAAiBV,EAAY,OAAOA,GAAa,SAAWA,EAAWA,EAAS,OAAU,KAC1FW,EAAiBX,EAAY,OAAOA,GAAa,SAAWA,EAAWA,EAAS,OAAU,KAG1FY,EAAUH,EAAO,EAAK,EAC5BI,EAAU,KACRD,EAAQ,QAAU,GACX,IAAM,KAAMA,EAAQ,QAAU,IACtC,EAGD,KAAM,CAACE,EAAcC,EAAcC,CAAY,EAAIC,EAAQ,IAAM,CAC/D,MAAMtB,EAAW,IAAM,CACjB,GAAA,CAACa,EAAM,QAAQ,QAAS,OACtB,KAAA,CAAE,KAAAU,EAAM,IAAAC,EAAK,MAAAC,EAAO,OAAAC,EAAQ,OAAAC,EAAQ,MAAAC,EAAO,EAAAC,EAAG,EAAAC,CAAE,EACpDjB,EAAM,QAAQ,QAAQ,sBAAsB,EAExCkB,EAAO,CACX,KAAAR,EACA,IAAAC,EACA,MAAAC,EACA,OAAAC,EACA,OAAAC,EACA,MAAAC,EACA,EAAAC,EACA,EAAAC,CACF,EAEIjB,EAAM,QAAQ,mBAAmB,aAAeL,IAC7CuB,EAAA,OAASlB,EAAM,QAAQ,QAAQ,aAC/BkB,EAAA,MAAQlB,EAAM,QAAQ,QAAQ,aAGrC,OAAO,OAAOkB,CAAI,EACdd,EAAQ,SAAW,CAACe,EAAenB,EAAM,QAAQ,WAAYkB,CAAI,GAAGpB,EAAKE,EAAM,QAAQ,WAAakB,CAAK,CAC/G,EACO,MAAA,CACL/B,EACAgB,EAAiBjB,EAAeC,EAAUgB,CAAc,EAAIhB,EAC5De,EAAiBhB,EAAeC,EAAUe,CAAc,EAAIf,CAC9D,GACC,CAACW,EAAKH,EAAYO,EAAgBC,CAAc,CAAC,EAGpD,SAASiB,GAAkB,CACrBpB,EAAM,QAAQ,mBACVA,EAAA,QAAQ,iBAAiB,QAASqB,GAAYA,EAAQ,oBAAoB,SAAUb,EAAc,EAAI,CAAC,EAC7GR,EAAM,QAAQ,iBAAmB,MAG/BA,EAAM,QAAQ,iBACVA,EAAA,QAAQ,eAAe,WAAW,EACxCA,EAAM,QAAQ,eAAiB,MAG7BA,EAAM,QAAQ,qBACZ,gBAAiB,QAAU,wBAAyB,OAAO,YAC7D,OAAO,YAAY,oBAAoB,SAAUA,EAAM,QAAQ,kBAAkB,EACxE,wBAAyB,QAClC,OAAO,oBAAoB,oBAAqBA,EAAM,QAAQ,kBAAkB,EAEpF,CAIF,SAASsB,GAAe,CACjBtB,EAAM,QAAQ,UACnBA,EAAM,QAAQ,eAAiB,IAAIJ,EAAeY,CAAY,EAC9DR,EAAM,QAAQ,eAAgB,QAAQA,EAAM,QAAQ,OAAO,EACvDP,GAAUO,EAAM,QAAQ,kBAC1BA,EAAM,QAAQ,iBAAiB,QAASuB,GACtCA,EAAgB,iBAAiB,SAAUf,EAAc,CAAE,QAAS,GAAM,QAAS,EAAM,CAAA,CAC3F,EAIIR,EAAA,QAAQ,mBAAqB,IAAM,CAC1BQ,EAAA,CACf,EAGI,gBAAiB,QAAU,qBAAsB,OAAO,YAC1D,OAAO,YAAY,iBAAiB,SAAUR,EAAM,QAAQ,kBAAkB,EACrE,wBAAyB,QAElC,OAAO,iBAAiB,oBAAqBA,EAAM,QAAQ,kBAAkB,EAC/E,CAIIwB,MAAAA,EAAOC,GAAkC,CACzC,CAACA,GAAQA,IAASzB,EAAM,QAAQ,UACpBoB,EAAA,EAChBpB,EAAM,QAAQ,QAAUyB,EAClBzB,EAAA,QAAQ,iBAAmB0B,EAAqBD,CAAI,EAC7CH,EAAA,EACf,EAGkBK,OAAAA,EAAAnB,EAAc,CAAQf,CAAAA,CAAO,EAC/CmC,EAAkBrB,CAAY,EAG9BF,EAAU,IAAM,CACEe,EAAA,EACHE,EAAA,CACZ,EAAA,CAAC7B,EAAQe,EAAcD,CAAY,CAAC,EAG7BF,EAAA,IAAMe,EAAiB,EAAE,EAC5B,CAACI,EAAK3B,EAAQS,CAAY,CACnC,CAGA,SAASsB,EAAkBC,EAAwC,CACjExB,EAAU,IAAM,CACd,MAAMyB,EAAKD,EACJ,OAAA,OAAA,iBAAiB,SAAUC,CAAE,EAC7B,IAAM,KAAK,OAAO,oBAAoB,SAAUA,CAAE,CAAA,EACxD,CAACD,CAAc,CAAC,CACrB,CACA,SAASF,EAAkBI,EAAsBC,EAAkB,CACjE3B,EAAU,IAAM,CACd,GAAI2B,EAAS,CACX,MAAMF,EAAKC,EACJ,OAAA,OAAA,iBAAiB,SAAUD,EAAI,CAAE,QAAS,GAAM,QAAS,GAAM,EAC/D,IAAM,KAAK,OAAO,oBAAoB,SAAUA,EAAI,EAAI,CAAA,CACjE,EACC,CAACC,EAAUC,CAAO,CAAC,CACxB,CAGA,SAASN,EAAqBL,EAAsD,CAClF,MAAMY,EAA6B,CAAC,EACpC,GAAI,CAACZ,GAAWA,IAAY,SAAS,KAAaY,OAAAA,EAC5C,KAAA,CAAE,SAAAC,EAAU,UAAAC,EAAW,UAAAC,CAAc,EAAA,OAAO,iBAAiBf,CAAO,EACtE,MAAA,CAACa,EAAUC,EAAWC,CAAS,EAAE,KAAMC,GAASA,IAAS,QAAUA,IAAS,QAAQ,GAAGJ,EAAO,KAAKZ,CAAO,EACvG,CAAC,GAAGY,EAAQ,GAAGP,EAAqBL,EAAQ,aAAa,CAAC,CACnE,CAGA,MAAMiB,EAA+B,CAAC,IAAK,IAAK,MAAO,SAAU,OAAQ,QAAS,QAAS,QAAQ,EAC7FnB,EAAiB,CAACoB,EAAiBC,IAA6BF,EAAK,MAAOG,GAAQF,EAAEE,CAAG,IAAMD,EAAEC,CAAG,CAAC"}