{"engines": {"node": ">=0.4.7"}, "repository": {"url": "git://github.com/frodwith/node-init.git", "type": "git"}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON>"}], "version": "0.1.2", "licences": [{"url": "http://www.opensource.org/licenses/mit-license", "type": "MIT License"}], "dependencies": {"daemon": ">=0.3.0"}, "name": "init", "bugs": "https://github.com/frodwith/node-init/issues", "author": "<PERSON> <<EMAIL>>", "description": "Turn your node daemon into an LSB-like init script", "main": "./init.js", "keywords": ["daemon", "init", "service", "LSB"], "devDependencies": {}, "contributors": [{"email": "<EMAIL>", "name": "<PERSON>"}]}