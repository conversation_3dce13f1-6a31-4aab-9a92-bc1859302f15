"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/quiz",{

/***/ "(pages-dir-browser)/./pages/quiz.tsx":
/*!************************!*\
  !*** ./pages/quiz.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst Quiz = ()=>{\n    var _this = undefined;\n    _s();\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quizData, setQuizData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sections = [\n        'Basic Info',\n        'Growth & Puberty Markers',\n        'Skeletal & Facial Development',\n        'Testosterone/Hormone Traits',\n        'Genetics & Family Traits',\n        'Lifestyle & Health',\n        'Psychological & Developmental',\n        'Ethnicity Background',\n        'Head and Skull Shape',\n        'Face Shape and Features',\n        'Eyes and Eyebrows',\n        'Final Consent'\n    ];\n    const updateQuizData = (field, value)=>{\n        setQuizData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const nextSection = ()=>{\n        if (currentSection < sections.length - 1) {\n            setCurrentSection(currentSection + 1);\n        }\n    };\n    const prevSection = ()=>{\n        if (currentSection > 0) {\n            setCurrentSection(currentSection - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        // Here you would typically send the data to your backend\n        console.log('Quiz Data:', quizData);\n        // Simulate API call\n        setTimeout(()=>{\n            alert('Quiz submitted! Check your email for results.');\n            setIsSubmitting(false);\n        }, 2000);\n    };\n    const renderQuestion = function(question, field) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null, type = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'radio';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-4\",\n                    children: question\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, _this),\n                type === 'radio' && options && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-3 cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"radio\",\n                                    name: field,\n                                    value: option,\n                                    checked: quizData[field] === option,\n                                    onChange: (e)=>updateQuizData(field, e.target.value),\n                                    className: \"w-4 h-4 text-[#00C2A8] bg-gray-700 border-gray-600 focus:ring-[#00C2A8]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-300\",\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, _this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, _this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, _this),\n                type === 'text' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: quizData[field] || '',\n                    onChange: (e)=>updateQuizData(field, e.target.value),\n                    className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#00C2A8] focus:border-transparent\",\n                    placeholder: \"Enter your answer...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, _this),\n                type === 'textarea' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: quizData[field] || '',\n                    onChange: (e)=>updateQuizData(field, e.target.value),\n                    rows: 3,\n                    className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#00C2A8] focus:border-transparent\",\n                    placeholder: \"Enter your answer...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, _this),\n                type === 'checkbox' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 cursor-pointer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            checked: quizData[field] || false,\n                            onChange: (e)=>updateQuizData(field, e.target.checked),\n                            className: \"w-4 h-4 text-[#00C2A8] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2A8]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-300\",\n                            children: \"Yes, I agree\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#141735] min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"BioAscension Quiz – Discover Your Genetic Potential\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Take our comprehensive genetic analysis quiz to discover your growth potential.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-30 bg-[#141735] text-white shadow flex items-center justify-between px-8 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/\",\n                        className: \"font-extrabold text-2xl tracking-tight flex items-center gap-2 hover:opacity-80 transition-opacity\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-transparent bg-clip-text\",\n                            children: \"BioAscension\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-400\",\n                        children: [\n                            \"Section \",\n                            currentSection + 1,\n                            \" of \",\n                            sections.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-700 h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-[#00C2A8] to-[#45B7D1] h-2 transition-all duration-300\",\n                    style: {\n                        width: \"\".concat((currentSection + 1) / sections.length * 100, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-6 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"professional-card rounded-2xl shadow-2xl p-8 md:p-12 text-white glow-effect\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-[#00C2A8] to-[#45B7D1] text-transparent bg-clip-text\",\n                                        children: sections[currentSection]\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Answer these questions to get your personalized genetic analysis\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                currentSection === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"What is your biological sex?\", \"biologicalSex\", [\n                                            \"Male\",\n                                            \"Female\"\n                                        ]),\n                                        renderQuestion(\"How old are you? (Exact age in years and months)\", \"age\", null, \"text\"),\n                                        renderQuestion(\"What is your current height? (Feet/inches or cm)\", \"currentHeight\", null, \"text\"),\n                                        renderQuestion(\"What is your current weight? (lbs or kg)\", \"currentWeight\", null, \"text\"),\n                                        renderQuestion(\"What country are you currently living in?\", \"country\", null, \"text\"),\n                                        renderQuestion(\"What time of day do you usually measure your height?\", \"measurementTime\", [\n                                            \"Morning\",\n                                            \"Night\",\n                                            \"Mixed\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"At what age did you notice signs of puberty beginning?\", \"pubertyAge\", null, \"text\"),\n                                        renderQuestion(\"Have you had a recent growth spurt?\", \"recentGrowthSpurt\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"How much have you grown in the past 12 months? (inches or cm)\", \"growthPast12Months\", null, \"text\"),\n                                        renderQuestion(\"Do you experience growing pains in legs or knees?\", \"growingPains\", [\n                                            \"Often\",\n                                            \"Sometimes\",\n                                            \"Rarely\",\n                                            \"Never\"\n                                        ]),\n                                        renderQuestion(\"Are your hands or feet still growing in size?\", \"handsFeetsGrowing\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Do you feel soreness or dull pain near knees, ankles, or wrists at night?\", \"nightSoreness\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Has your shoe size increased in the past year?\", \"shoeSizeIncrease\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"Has your jawline or facial structure noticeably matured recently?\", \"facialMaturation\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ]),\n                                        renderQuestion(\"Do you have noticeable Adam's apple growth?\", \"adamsApple\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Do you have facial hair?\", \"facialHair\", [\n                                            \"None\",\n                                            \"Light peach fuzz\",\n                                            \"Full beard/mustache growth\"\n                                        ]),\n                                        renderQuestion(\"Have your cheekbones become more prominent recently?\", \"cheekbones\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ]),\n                                        renderQuestion(\"Has your voice changed in pitch or cracked more often recently?\", \"voiceChange\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Has your overall body frame become wider (shoulders, chest)?\", \"bodyFrameWider\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"How would you describe your body hair?\", \"bodyHair\", [\n                                            \"Minimal\",\n                                            \"Moderate\",\n                                            \"Dense\"\n                                        ]),\n                                        renderQuestion(\"Do you gain muscle easily with workouts?\", \"muscleGain\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Somewhat\",\n                                            \"Unsure\",\n                                            \"Never tried\"\n                                        ]),\n                                        renderQuestion(\"Do you have acne or oily skin?\", \"acne\", [\n                                            \"Mild\",\n                                            \"Moderate\",\n                                            \"Severe\",\n                                            \"None\"\n                                        ]),\n                                        renderQuestion(\"How deep is your voice?\", \"voiceDepth\", [\n                                            \"High\",\n                                            \"Medium\",\n                                            \"Deep\"\n                                        ]),\n                                        renderQuestion(\"Do you sweat more than you did 1–2 years ago?\", \"sweating\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Are your armpits or groin hair increasing rapidly?\", \"armpitGroinHair\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Have you noticed changes in jaw or nose definition in the past year?\", \"jawNoseDefinition\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"What is your father's height?\", \"fatherHeight\", null, \"text\"),\n                                        renderQuestion(\"What is your mother's height?\", \"motherHeight\", null, \"text\"),\n                                        renderQuestion(\"Do you have siblings?\", \"hasSiblings\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"If yes, what is their gender, age, height?\", \"siblingInfo\", null, \"textarea\"),\n                                        renderQuestion(\"Did your parents hit puberty early, average, or late?\", \"parentsPublerty\", [\n                                            \"Early\",\n                                            \"Average\",\n                                            \"Late\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"What ethnic background are you from (or mix of)?\", \"ethnicity\", null, \"text\"),\n                                        renderQuestion(\"Has anyone in your family had late growth spurts (after age 17)?\", \"lateGrowthSpurts\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"How often do you exercise per week?\", \"exerciseFrequency\", [\n                                            \"0\",\n                                            \"1–2\",\n                                            \"3–5\",\n                                            \"6+ times\"\n                                        ]),\n                                        renderQuestion(\"How many hours of sleep do you get per night?\", \"sleepHours\", [\n                                            \"Less than 6\",\n                                            \"6–8\",\n                                            \"8+\"\n                                        ]),\n                                        renderQuestion(\"Do you currently take any supplements or medications?\", \"supplements\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"Do you drink milk or consume dairy regularly?\", \"dairy\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"Do you eat a protein-rich diet?\", \"proteinDiet\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Are you around cigarette smoke regularly?\", \"cigaretteSmoke\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"How often do you eat soy sauce or soy-based foods?\", \"soyFoods\", [\n                                            \"Often\",\n                                            \"Sometimes\",\n                                            \"Rarely\",\n                                            \"Never\"\n                                        ]),\n                                        renderQuestion(\"Are you currently experiencing a high-stress lifestyle?\", \"highStress\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Do you have visible abs?\", \"visibleAbs\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Do you feel more mature or taller than most of your peers?\", \"maturityComparison\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Same as others\"\n                                        ]),\n                                        renderQuestion(\"Are you still noticeably growing taller month to month?\", \"stillGrowing\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Do you feel like your puberty is mostly finished, midway, or barely started?\", \"pubertyStage\", [\n                                            \"Mostly finished\",\n                                            \"Midway\",\n                                            \"Barely started\"\n                                        ]),\n                                        renderQuestion(\"Do you often compare your features to older siblings or parents?\", \"compareToFamily\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Are your voice, feet, or facial structure more mature than your friends?\", \"maturityVsFriends\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"About the same\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 7 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 p-4 bg-gray-800 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Why does your ethnicity matter?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Because your ethnic background plays a key role in predicting height. Genetics determine how many growth spurts you'll have, how long you'll grow, when your growth plates will close, and more. By understanding this, we can estimate your final height with top tier accuracy.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        renderQuestion(\"What country were you born in?\", \"birthCountry\", null, \"text\"),\n                                        renderQuestion(\"What countries are your parents from?\", \"parentsCountries\", null, \"text\"),\n                                        renderQuestion(\"Where were your grandparents born?\", \"grandparentsCountries\", null, \"text\"),\n                                        renderQuestion(\"What ethnic group is your family part of? (For example: Chinese, Indian, Arab, African, European, etc.)\", \"ethnicGroup\", null, \"text\"),\n                                        renderQuestion(\"Do you know if your family is mixed with more than one ethnicity?\", \"mixedEthnicity\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Is your head more round, long, or somewhere in between?\", \"headShape\", [\n                                            \"Round\",\n                                            \"Long\",\n                                            \"In between\"\n                                        ]),\n                                        renderQuestion(\"Is the top of your head flat, rounded, or high?\", \"headTop\", [\n                                            \"Flat\",\n                                            \"Rounded\",\n                                            \"High\"\n                                        ]),\n                                        renderQuestion(\"Does the back of your head stick out a lot or is it flat?\", \"headBack\", [\n                                            \"Sticks out a lot\",\n                                            \"Flat\",\n                                            \"Somewhere in between\"\n                                        ]),\n                                        renderQuestion(\"Is your forehead straight up, curved back, or kind of slanted?\", \"forehead\", [\n                                            \"Straight up\",\n                                            \"Curved back\",\n                                            \"Slanted\"\n                                        ]),\n                                        renderQuestion(\"Is your head wider at the top or at the jaw?\", \"headWidth\", [\n                                            \"Wider at top\",\n                                            \"Wider at jaw\",\n                                            \"About the same\"\n                                        ]),\n                                        renderQuestion(\"Do you feel a raised line running down the top of your head?\", \"raisedLine\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 9 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Is your face more long and narrow, or short and wide?\", \"faceShape\", [\n                                            \"Long and narrow\",\n                                            \"Short and wide\",\n                                            \"Somewhere in between\"\n                                        ]),\n                                        renderQuestion(\"Do your cheekbones stick out, or are they flat?\", \"cheekbonesProminence\", [\n                                            \"Stick out\",\n                                            \"Flat\",\n                                            \"Slightly prominent\"\n                                        ]),\n                                        renderQuestion(\"Do you have a strong bone above your eyebrows (brow bone)?\", \"browBone\", [\n                                            \"Yes, strong\",\n                                            \"No, flat\",\n                                            \"Slightly prominent\"\n                                        ]),\n                                        renderQuestion(\"Is your nose flat and wide, medium, or tall and narrow?\", \"noseShape\", [\n                                            \"Flat and wide\",\n                                            \"Medium\",\n                                            \"Tall and narrow\"\n                                        ]),\n                                        renderQuestion(\"Are your nostrils round or more like slits?\", \"nostrils\", [\n                                            \"Round\",\n                                            \"Slit-like\",\n                                            \"In between\"\n                                        ]),\n                                        renderQuestion(\"Is there a lot of space between your eyes or just a little?\", \"eyeSpacing\", [\n                                            \"A lot of space\",\n                                            \"Little space\",\n                                            \"Normal spacing\"\n                                        ]),\n                                        renderQuestion(\"Is your chin pointy, round, or big and square?\", \"chinShape\", [\n                                            \"Pointy\",\n                                            \"Round\",\n                                            \"Big and square\"\n                                        ]),\n                                        renderQuestion(\"Is your jawline sharp, curved, or soft-looking?\", \"jawlineShape\", [\n                                            \"Sharp\",\n                                            \"Curved\",\n                                            \"Soft-looking\"\n                                        ]),\n                                        renderQuestion(\"Does your face stick out in the middle (like your nose area) or is it more flat?\", \"faceProfile\", [\n                                            \"Sticks out\",\n                                            \"Flat\",\n                                            \"Slightly prominent\"\n                                        ]),\n                                        renderQuestion(\"Is the area between your nose and upper lip long or short?\", \"noseLipSpace\", [\n                                            \"Long\",\n                                            \"Short\",\n                                            \"Medium\"\n                                        ]),\n                                        renderQuestion(\"Does your face grow more forward or downward?\", \"faceGrowthDirection\", [\n                                            \"Forward\",\n                                            \"Downward\",\n                                            \"Both equally\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"What color are your eyes naturally?\", \"eyeColor\", [\n                                            \"Brown\",\n                                            \"Blue\",\n                                            \"Green\",\n                                            \"Hazel\",\n                                            \"Gray\",\n                                            \"Other\"\n                                        ]),\n                                        renderQuestion(\"Do you have an inner eyelid fold (like a monolid)?\", \"eyelidFold\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Partially\"\n                                        ]),\n                                        renderQuestion(\"Are your eyes deep-set or do they stick out more?\", \"eyeDepth\", [\n                                            \"Deep-set\",\n                                            \"Stick out\",\n                                            \"Normal\"\n                                        ]),\n                                        renderQuestion(\"Do you have double eyelids, single eyelids, or something in between?\", \"eyelidType\", [\n                                            \"Double eyelids\",\n                                            \"Single eyelids\",\n                                            \"In between\"\n                                        ]),\n                                        renderQuestion(\"Are your eyebrows straight, arched, or angled?\", \"eyebrowShape\", [\n                                            \"Straight\",\n                                            \"Arched\",\n                                            \"Angled\"\n                                        ]),\n                                        renderQuestion(\"How much space is there between your eyebrows and your eyes?\", \"eyebrowEyeSpace\", [\n                                            \"A lot of space\",\n                                            \"Little space\",\n                                            \"Normal spacing\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 11 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8 p-6 bg-gradient-to-r from-[#00C2A8]/10 to-[#45B7D1]/10 rounded-lg border border-[#00C2A8]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-white mb-4\",\n                                                    children: \"\\uD83C\\uDF89 Almost Done!\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-4\",\n                                                    children: \"You're about to receive your personalized genetic height analysis report. This comprehensive report will include your growth potential, genetic markers, and personalized recommendations based on your unique profile.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        renderQuestion(\"What is your Gmail address to receive your results?\", \"email\", null, \"text\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Final Consent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-gray-800 rounded-lg mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: \"By proceeding, you agree to receive your personalized genetic analysis report and insights. Your data will be processed securely and used only for generating your personalized report.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                renderQuestion(\"Do you agree to receive your personalized report and insights?\", \"consent\", null, \"checkbox\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-12 pt-8 border-t border-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevSection,\n                                    disabled: currentSection === 0,\n                                    className: \"px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, undefined),\n                                currentSection === sections.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSubmit,\n                                    disabled: isSubmitting || !quizData.email || !quizData.consent,\n                                    className: \"cta-button px-8 py-3 rounded-lg font-bold disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isSubmitting ? 'Processing...' : 'Reveal Your Blueprint – $1'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextSection,\n                                    className: \"cta-button px-6 py-3 rounded-lg font-semibold\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Quiz, \"a9uD8aaU7wDLfRD+Wizm8/u2SFU=\");\n_c = Quiz;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Quiz);\nvar _c;\n$RefreshReg$(_c, \"Quiz\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/quiz.tsx\n"));

/***/ })

});