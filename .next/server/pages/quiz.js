/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/quiz";
exports.ids = ["pages/quiz"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fquiz&preferredRegion=&absolutePagePath=.%2Fpages%2Fquiz.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fquiz&preferredRegion=&absolutePagePath=.%2Fpages%2Fquiz.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/quiz.tsx */ \"(pages-dir-node)/./pages/quiz.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/quiz\",\n        pathname: \"/quiz\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_quiz_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fquiz&preferredRegion=&absolutePagePath=.%2Fpages%2Fquiz.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/_app.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/_app.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRCO0FBQ0U7QUFFZixTQUFTQyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELHFCQUNFOzswQkFDRSw4REFBQ0gsa0RBQUlBOzBCQUNILDRFQUFDSTtvQkFBS0MsTUFBSztvQkFBb0ZDLEtBQUk7Ozs7Ozs7Ozs7OzBCQUVyRyw4REFBQ0o7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9leGFtL0Rlc2t0b3AvcHJvamVjdHMvYmlvYXNjZW5zaW9uL2Jpb2FzY2Vuc2lvbi9wYWdlcy9fYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnXG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPGxpbmsgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9TW9udHNlcnJhdDp3Z2h0QDQwMDs2MDA7NzAwJmRpc3BsYXk9c3dhcFwiIHJlbD1cInN0eWxlc2hlZXRcIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvPlxuICApXG59Il0sIm5hbWVzIjpbIkhlYWQiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJsaW5rIiwiaHJlZiIsInJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/quiz.tsx":
/*!************************!*\
  !*** ./pages/quiz.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Quiz = ()=>{\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quizData, setQuizData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sections = [\n        'Basic Info',\n        'Growth & Puberty Markers',\n        'Skeletal & Facial Development',\n        'Testosterone/Hormone Traits',\n        'Genetics & Family Traits',\n        'Lifestyle & Health',\n        'Psychological & Developmental',\n        'Ethnicity Background',\n        'Head and Skull Shape',\n        'Face Shape and Features',\n        'Eyes and Eyebrows',\n        'Final Consent'\n    ];\n    const updateQuizData = (field, value)=>{\n        setQuizData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const nextSection = ()=>{\n        if (currentSection < sections.length - 1) {\n            setCurrentSection(currentSection + 1);\n        }\n    };\n    const prevSection = ()=>{\n        if (currentSection > 0) {\n            setCurrentSection(currentSection - 1);\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        // Here you would typically send the data to your backend\n        console.log('Quiz Data:', quizData);\n        // Simulate API call\n        setTimeout(()=>{\n            alert('Quiz submitted! Check your email for results.');\n            setIsSubmitting(false);\n        }, 2000);\n    };\n    const renderQuestion = (question, field, options = null, type = 'radio')=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-4\",\n                    children: question\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined),\n                type === 'radio' && options && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-3 cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"radio\",\n                                    name: field,\n                                    value: option,\n                                    checked: quizData[field] === option,\n                                    onChange: (e)=>updateQuizData(field, e.target.value),\n                                    className: \"w-4 h-4 text-[#00C2A8] bg-gray-700 border-gray-600 focus:ring-[#00C2A8]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-300\",\n                                    children: option\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, undefined),\n                type === 'text' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: quizData[field] || '',\n                    onChange: (e)=>updateQuizData(field, e.target.value),\n                    className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#00C2A8] focus:border-transparent\",\n                    placeholder: \"Enter your answer...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, undefined),\n                type === 'textarea' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: quizData[field] || '',\n                    onChange: (e)=>updateQuizData(field, e.target.value),\n                    rows: 3,\n                    className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#00C2A8] focus:border-transparent\",\n                    placeholder: \"Enter your answer...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined),\n                type === 'checkbox' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 cursor-pointer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            checked: quizData[field] || false,\n                            onChange: (e)=>updateQuizData(field, e.target.checked),\n                            className: \"w-4 h-4 text-[#00C2A8] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2A8]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-300\",\n                            children: \"Yes, I agree\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#141735] min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"BioAscension Quiz – Discover Your Genetic Potential\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Take our comprehensive genetic analysis quiz to discover your growth potential.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-30 bg-[#141735] text-white shadow flex items-center justify-between px-8 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-extrabold text-2xl tracking-tight flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-transparent bg-clip-text\",\n                            children: \"BioAscension\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-400\",\n                        children: [\n                            \"Section \",\n                            currentSection + 1,\n                            \" of \",\n                            sections.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-700 h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-[#00C2A8] to-[#45B7D1] h-2 transition-all duration-300\",\n                    style: {\n                        width: `${(currentSection + 1) / sections.length * 100}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-6 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"professional-card rounded-2xl shadow-2xl p-8 md:p-12 text-white glow-effect\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-[#00C2A8] to-[#45B7D1] text-transparent bg-clip-text\",\n                                        children: sections[currentSection]\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Answer these questions to get your personalized genetic analysis\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                currentSection === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"What is your biological sex?\", \"biologicalSex\", [\n                                            \"Male\",\n                                            \"Female\"\n                                        ]),\n                                        renderQuestion(\"How old are you? (Exact age in years and months)\", \"age\", null, \"text\"),\n                                        renderQuestion(\"What is your current height? (Feet/inches or cm)\", \"currentHeight\", null, \"text\"),\n                                        renderQuestion(\"What is your current weight? (lbs or kg)\", \"currentWeight\", null, \"text\"),\n                                        renderQuestion(\"What country are you currently living in?\", \"country\", null, \"text\"),\n                                        renderQuestion(\"What time of day do you usually measure your height?\", \"measurementTime\", [\n                                            \"Morning\",\n                                            \"Night\",\n                                            \"Mixed\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"At what age did you notice signs of puberty beginning?\", \"pubertyAge\", null, \"text\"),\n                                        renderQuestion(\"Have you had a recent growth spurt?\", \"recentGrowthSpurt\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"How much have you grown in the past 12 months? (inches or cm)\", \"growthPast12Months\", null, \"text\"),\n                                        renderQuestion(\"Do you experience growing pains in legs or knees?\", \"growingPains\", [\n                                            \"Often\",\n                                            \"Sometimes\",\n                                            \"Rarely\",\n                                            \"Never\"\n                                        ]),\n                                        renderQuestion(\"Are your hands or feet still growing in size?\", \"handsFeetsGrowing\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Do you feel soreness or dull pain near knees, ankles, or wrists at night?\", \"nightSoreness\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Has your shoe size increased in the past year?\", \"shoeSizeIncrease\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"Has your jawline or facial structure noticeably matured recently?\", \"facialMaturation\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ]),\n                                        renderQuestion(\"Do you have noticeable Adam's apple growth?\", \"adamsApple\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Do you have facial hair?\", \"facialHair\", [\n                                            \"None\",\n                                            \"Light peach fuzz\",\n                                            \"Full beard/mustache growth\"\n                                        ]),\n                                        renderQuestion(\"Have your cheekbones become more prominent recently?\", \"cheekbones\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ]),\n                                        renderQuestion(\"Has your voice changed in pitch or cracked more often recently?\", \"voiceChange\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Has your overall body frame become wider (shoulders, chest)?\", \"bodyFrameWider\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"How would you describe your body hair?\", \"bodyHair\", [\n                                            \"Minimal\",\n                                            \"Moderate\",\n                                            \"Dense\"\n                                        ]),\n                                        renderQuestion(\"Do you gain muscle easily with workouts?\", \"muscleGain\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Somewhat\",\n                                            \"Unsure\",\n                                            \"Never tried\"\n                                        ]),\n                                        renderQuestion(\"Do you have acne or oily skin?\", \"acne\", [\n                                            \"Mild\",\n                                            \"Moderate\",\n                                            \"Severe\",\n                                            \"None\"\n                                        ]),\n                                        renderQuestion(\"How deep is your voice?\", \"voiceDepth\", [\n                                            \"High\",\n                                            \"Medium\",\n                                            \"Deep\"\n                                        ]),\n                                        renderQuestion(\"Do you sweat more than you did 1–2 years ago?\", \"sweating\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Are your armpits or groin hair increasing rapidly?\", \"armpitGroinHair\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Have you noticed changes in jaw or nose definition in the past year?\", \"jawNoseDefinition\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Slightly\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"What is your father's height?\", \"fatherHeight\", null, \"text\"),\n                                        renderQuestion(\"What is your mother's height?\", \"motherHeight\", null, \"text\"),\n                                        renderQuestion(\"Do you have siblings?\", \"hasSiblings\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"If yes, what is their gender, age, height?\", \"siblingInfo\", null, \"textarea\"),\n                                        renderQuestion(\"Did your parents hit puberty early, average, or late?\", \"parentsPublerty\", [\n                                            \"Early\",\n                                            \"Average\",\n                                            \"Late\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"What ethnic background are you from (or mix of)?\", \"ethnicity\", null, \"text\"),\n                                        renderQuestion(\"Has anyone in your family had late growth spurts (after age 17)?\", \"lateGrowthSpurts\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"How often do you exercise per week?\", \"exerciseFrequency\", [\n                                            \"0\",\n                                            \"1–2\",\n                                            \"3–5\",\n                                            \"6+ times\"\n                                        ]),\n                                        renderQuestion(\"How many hours of sleep do you get per night?\", \"sleepHours\", [\n                                            \"Less than 6\",\n                                            \"6–8\",\n                                            \"8+\"\n                                        ]),\n                                        renderQuestion(\"Do you currently take any supplements or medications?\", \"supplements\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"Do you drink milk or consume dairy regularly?\", \"dairy\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"Do you eat a protein-rich diet?\", \"proteinDiet\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Are you around cigarette smoke regularly?\", \"cigaretteSmoke\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ]),\n                                        renderQuestion(\"How often do you eat soy sauce or soy-based foods?\", \"soyFoods\", [\n                                            \"Often\",\n                                            \"Sometimes\",\n                                            \"Rarely\",\n                                            \"Never\"\n                                        ]),\n                                        renderQuestion(\"Are you currently experiencing a high-stress lifestyle?\", \"highStress\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Do you have visible abs?\", \"visibleAbs\", [\n                                            \"Yes\",\n                                            \"No\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Do you feel more mature or taller than most of your peers?\", \"maturityComparison\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Same as others\"\n                                        ]),\n                                        renderQuestion(\"Are you still noticeably growing taller month to month?\", \"stillGrowing\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ]),\n                                        renderQuestion(\"Do you feel like your puberty is mostly finished, midway, or barely started?\", \"pubertyStage\", [\n                                            \"Mostly finished\",\n                                            \"Midway\",\n                                            \"Barely started\"\n                                        ]),\n                                        renderQuestion(\"Do you often compare your features to older siblings or parents?\", \"compareToFamily\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Sometimes\"\n                                        ]),\n                                        renderQuestion(\"Are your voice, feet, or facial structure more mature than your friends?\", \"maturityVsFriends\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"About the same\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 7 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6 p-4 bg-gray-800 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Why does your ethnicity matter?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Because your ethnic background plays a key role in predicting height. Genetics determine how many growth spurts you'll have, how long you'll grow, when your growth plates will close, and more. By understanding this, we can estimate your final height with top tier accuracy.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        renderQuestion(\"What country were you born in?\", \"birthCountry\", null, \"text\"),\n                                        renderQuestion(\"What countries are your parents from?\", \"parentsCountries\", null, \"text\"),\n                                        renderQuestion(\"Where were your grandparents born?\", \"grandparentsCountries\", null, \"text\"),\n                                        renderQuestion(\"What ethnic group is your family part of? (For example: Chinese, Indian, Arab, African, European, etc.)\", \"ethnicGroup\", null, \"text\"),\n                                        renderQuestion(\"Do you know if your family is mixed with more than one ethnicity?\", \"mixedEthnicity\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Is your head more round, long, or somewhere in between?\", \"headShape\", [\n                                            \"Round\",\n                                            \"Long\",\n                                            \"In between\"\n                                        ]),\n                                        renderQuestion(\"Is the top of your head flat, rounded, or high?\", \"headTop\", [\n                                            \"Flat\",\n                                            \"Rounded\",\n                                            \"High\"\n                                        ]),\n                                        renderQuestion(\"Does the back of your head stick out a lot or is it flat?\", \"headBack\", [\n                                            \"Sticks out a lot\",\n                                            \"Flat\",\n                                            \"Somewhere in between\"\n                                        ]),\n                                        renderQuestion(\"Is your forehead straight up, curved back, or kind of slanted?\", \"forehead\", [\n                                            \"Straight up\",\n                                            \"Curved back\",\n                                            \"Slanted\"\n                                        ]),\n                                        renderQuestion(\"Is your head wider at the top or at the jaw?\", \"headWidth\", [\n                                            \"Wider at top\",\n                                            \"Wider at jaw\",\n                                            \"About the same\"\n                                        ]),\n                                        renderQuestion(\"Do you feel a raised line running down the top of your head?\", \"raisedLine\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Not sure\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 9 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"Is your face more long and narrow, or short and wide?\", \"faceShape\", [\n                                            \"Long and narrow\",\n                                            \"Short and wide\",\n                                            \"Somewhere in between\"\n                                        ]),\n                                        renderQuestion(\"Do your cheekbones stick out, or are they flat?\", \"cheekbonesProminence\", [\n                                            \"Stick out\",\n                                            \"Flat\",\n                                            \"Slightly prominent\"\n                                        ]),\n                                        renderQuestion(\"Do you have a strong bone above your eyebrows (brow bone)?\", \"browBone\", [\n                                            \"Yes, strong\",\n                                            \"No, flat\",\n                                            \"Slightly prominent\"\n                                        ]),\n                                        renderQuestion(\"Is your nose flat and wide, medium, or tall and narrow?\", \"noseShape\", [\n                                            \"Flat and wide\",\n                                            \"Medium\",\n                                            \"Tall and narrow\"\n                                        ]),\n                                        renderQuestion(\"Are your nostrils round or more like slits?\", \"nostrils\", [\n                                            \"Round\",\n                                            \"Slit-like\",\n                                            \"In between\"\n                                        ]),\n                                        renderQuestion(\"Is there a lot of space between your eyes or just a little?\", \"eyeSpacing\", [\n                                            \"A lot of space\",\n                                            \"Little space\",\n                                            \"Normal spacing\"\n                                        ]),\n                                        renderQuestion(\"Is your chin pointy, round, or big and square?\", \"chinShape\", [\n                                            \"Pointy\",\n                                            \"Round\",\n                                            \"Big and square\"\n                                        ]),\n                                        renderQuestion(\"Is your jawline sharp, curved, or soft-looking?\", \"jawlineShape\", [\n                                            \"Sharp\",\n                                            \"Curved\",\n                                            \"Soft-looking\"\n                                        ]),\n                                        renderQuestion(\"Does your face stick out in the middle (like your nose area) or is it more flat?\", \"faceProfile\", [\n                                            \"Sticks out\",\n                                            \"Flat\",\n                                            \"Slightly prominent\"\n                                        ]),\n                                        renderQuestion(\"Is the area between your nose and upper lip long or short?\", \"noseLipSpace\", [\n                                            \"Long\",\n                                            \"Short\",\n                                            \"Medium\"\n                                        ]),\n                                        renderQuestion(\"Does your face grow more forward or downward?\", \"faceGrowthDirection\", [\n                                            \"Forward\",\n                                            \"Downward\",\n                                            \"Both equally\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        renderQuestion(\"What color are your eyes naturally?\", \"eyeColor\", [\n                                            \"Brown\",\n                                            \"Blue\",\n                                            \"Green\",\n                                            \"Hazel\",\n                                            \"Gray\",\n                                            \"Other\"\n                                        ]),\n                                        renderQuestion(\"Do you have an inner eyelid fold (like a monolid)?\", \"eyelidFold\", [\n                                            \"Yes\",\n                                            \"No\",\n                                            \"Partially\"\n                                        ]),\n                                        renderQuestion(\"Are your eyes deep-set or do they stick out more?\", \"eyeDepth\", [\n                                            \"Deep-set\",\n                                            \"Stick out\",\n                                            \"Normal\"\n                                        ]),\n                                        renderQuestion(\"Do you have double eyelids, single eyelids, or something in between?\", \"eyelidType\", [\n                                            \"Double eyelids\",\n                                            \"Single eyelids\",\n                                            \"In between\"\n                                        ]),\n                                        renderQuestion(\"Are your eyebrows straight, arched, or angled?\", \"eyebrowShape\", [\n                                            \"Straight\",\n                                            \"Arched\",\n                                            \"Angled\"\n                                        ]),\n                                        renderQuestion(\"How much space is there between your eyebrows and your eyes?\", \"eyebrowEyeSpace\", [\n                                            \"A lot of space\",\n                                            \"Little space\",\n                                            \"Normal spacing\"\n                                        ])\n                                    ]\n                                }, void 0, true),\n                                currentSection === 11 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8 p-6 bg-gradient-to-r from-[#00C2A8]/10 to-[#45B7D1]/10 rounded-lg border border-[#00C2A8]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-white mb-4\",\n                                                    children: \"\\uD83C\\uDF89 Almost Done!\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-4\",\n                                                    children: \"You're about to receive your personalized genetic height analysis report. This comprehensive report will include your growth potential, genetic markers, and personalized recommendations based on your unique profile.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        renderQuestion(\"What is your Gmail address to receive your results?\", \"email\", null, \"text\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Final Consent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-gray-800 rounded-lg mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: \"By proceeding, you agree to receive your personalized genetic analysis report and insights. Your data will be processed securely and used only for generating your personalized report.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                renderQuestion(\"Do you agree to receive your personalized report and insights?\", \"consent\", null, \"checkbox\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-12 pt-8 border-t border-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevSection,\n                                    disabled: currentSection === 0,\n                                    className: \"px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, undefined),\n                                currentSection === sections.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSubmit,\n                                    disabled: isSubmitting || !quizData.email || !quizData.consent,\n                                    className: \"cta-button px-8 py-3 rounded-lg font-bold disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isSubmitting ? 'Processing...' : 'Reveal Your Blueprint – $1'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextSection,\n                                    className: \"cta-button px-6 py-3 rounded-lg font-semibold\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/quiz.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Quiz);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/quiz.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fquiz&preferredRegion=&absolutePagePath=.%2Fpages%2Fquiz.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();