import React, { useState } from 'react';
import Head from 'next/head';

interface QuizData {
  // Basic Info
  biologicalSex: string;
  age: string;
  currentHeight: string;
  currentWeight: string;
  country: string;
  measurementTime: string;
  
  // Growth & Puberty Markers
  pubertyAge: string;
  recentGrowthSpurt: string;
  growthPast12Months: string;
  growingPains: string;
  handsFeetsGrowing: string;
  nightSoreness: string;
  
  // Skeletal & Facial Development
  shoeSizeIncrease: string;
  facialMaturation: string;
  adamsApple: string;
  facialHair: string;
  cheekbones: string;
  voiceChange: string;
  bodyFrameWider: string;
  
  // Testosterone/Hormone Traits
  bodyHair: string;
  muscleGain: string;
  acne: string;
  voiceDepth: string;
  sweating: string;
  armpitGroinHair: string;
  jawNoseDefinition: string;
  
  // Genetics & Family Traits
  fatherHeight: string;
  motherHeight: string;
  hasSiblings: string;
  siblingInfo: string;
  parentsPublerty: string;
  ethnicity: string;
  lateGrowthSpurts: string;
  
  // Lifestyle & Health
  exerciseFrequency: string;
  sleepHours: string;
  supplements: string;
  dairy: string;
  proteinDiet: string;
  cigaretteSmoke: string;
  soyFoods: string;
  highStress: string;
  visibleAbs: string;
  
  // Psychological & Developmental
  maturityComparison: string;
  stillGrowing: string;
  pubertyStage: string;
  compareToFamily: string;
  maturityVsFriends: string;
  
  // Ethnicity Background
  birthCountry: string;
  parentsCountries: string;
  grandparentsCountries: string;
  ethnicGroup: string;
  mixedEthnicity: string;
  
  // Head and Skull Shape
  headShape: string;
  headTop: string;
  headBack: string;
  forehead: string;
  headWidth: string;
  raisedLine: string;
  
  // Face Shape and Features
  faceShape: string;
  cheekbonesProminence: string;
  browBone: string;
  noseShape: string;
  nostrils: string;
  eyeSpacing: string;
  chinShape: string;
  jawlineShape: string;
  faceProfile: string;
  noseLipSpace: string;
  faceGrowthDirection: string;
  
  // Eyes and Eyebrows
  eyeColor: string;
  eyelidFold: string;
  eyeDepth: string;
  eyelidType: string;
  eyebrowShape: string;
  eyebrowEyeSpace: string;
  
  // Final
  email: string;
  consent: boolean;
}

const Quiz: React.FC = () => {
  const [currentSection, setCurrentSection] = useState(0);
  const [quizData, setQuizData] = useState<QuizData>({} as QuizData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const sections = [
    'Basic Info',
    'Growth & Puberty Markers', 
    'Skeletal & Facial Development',
    'Testosterone/Hormone Traits',
    'Genetics & Family Traits',
    'Lifestyle & Health',
    'Psychological & Developmental',
    'Ethnicity Background',
    'Head and Skull Shape',
    'Face Shape and Features',
    'Eyes and Eyebrows',
    'Final Consent'
  ];

  const updateQuizData = (field: keyof QuizData, value: string | boolean) => {
    setQuizData(prev => ({ ...prev, [field]: value }));
  };

  const nextSection = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const prevSection = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    // Here you would typically send the data to your backend
    console.log('Quiz Data:', quizData);
    
    // Simulate API call
    setTimeout(() => {
      alert('Quiz submitted! Check your email for results.');
      setIsSubmitting(false);
    }, 2000);
  };

  const renderQuestion = (
    question: string,
    field: keyof QuizData,
    options: string[] | null = null,
    type: 'radio' | 'text' | 'textarea' | 'checkbox' = 'radio'
  ) => {
    return (
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-white mb-4">{question}</h3>
        {type === 'radio' && options && (
          <div className="space-y-3">
            {options.map((option, index) => (
              <label key={index} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name={field}
                  value={option}
                  checked={quizData[field] === option}
                  onChange={(e) => updateQuizData(field, e.target.value)}
                  className="w-4 h-4 text-[#00C2A8] bg-gray-700 border-gray-600 focus:ring-[#00C2A8]"
                />
                <span className="text-gray-300">{option}</span>
              </label>
            ))}
          </div>
        )}
        {type === 'text' && (
          <input
            type="text"
            value={quizData[field] as string || ''}
            onChange={(e) => updateQuizData(field, e.target.value)}
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#00C2A8] focus:border-transparent"
            placeholder="Enter your answer..."
          />
        )}
        {type === 'textarea' && (
          <textarea
            value={quizData[field] as string || ''}
            onChange={(e) => updateQuizData(field, e.target.value)}
            rows={3}
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-[#00C2A8] focus:border-transparent"
            placeholder="Enter your answer..."
          />
        )}
        {type === 'checkbox' && (
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={quizData[field] as boolean || false}
              onChange={(e) => updateQuizData(field, e.target.checked)}
              className="w-4 h-4 text-[#00C2A8] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2A8]"
            />
            <span className="text-gray-300">Yes, I agree</span>
          </label>
        )}
      </div>
    );
  };

  return (
    <div className="bg-[#141735] min-h-screen">
      <Head>
        <title>BioAscension Quiz – Discover Your Genetic Potential</title>
        <meta name="description" content="Take our comprehensive genetic analysis quiz to discover your growth potential." />
      </Head>

      {/* Header */}
      <header className="sticky top-0 z-30 bg-[#141735] text-white shadow flex items-center justify-between px-8 py-4">
        <div className="font-extrabold text-2xl tracking-tight flex items-center gap-2">
          <span className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-transparent bg-clip-text">BioAscension</span>
        </div>
        <div className="text-sm text-gray-400">
          Section {currentSection + 1} of {sections.length}
        </div>
      </header>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 h-2">
        <div 
          className="bg-gradient-to-r from-[#00C2A8] to-[#45B7D1] h-2 transition-all duration-300"
          style={{ width: `${((currentSection + 1) / sections.length) * 100}%` }}
        ></div>
      </div>

      {/* Quiz Content */}
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="professional-card rounded-2xl shadow-2xl p-8 md:p-12 text-white glow-effect">
          <div className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold mb-2">
              <span className="bg-gradient-to-r from-[#00C2A8] to-[#45B7D1] text-transparent bg-clip-text">
                {sections[currentSection]}
              </span>
            </h1>
            <p className="text-gray-400">Answer these questions to get your personalized genetic analysis</p>
          </div>

          <div className="space-y-6">
            {/* Basic Info */}
            {currentSection === 0 && (
              <>
                {renderQuestion("What is your biological sex?", "biologicalSex", ["Male", "Female"])}
                {renderQuestion("How old are you? (Exact age in years and months)", "age", null, "text")}
                {renderQuestion("What is your current height? (Feet/inches or cm)", "currentHeight", null, "text")}
                {renderQuestion("What is your current weight? (lbs or kg)", "currentWeight", null, "text")}
                {renderQuestion("What country are you currently living in?", "country", null, "text")}
                {renderQuestion("What time of day do you usually measure your height?", "measurementTime", ["Morning", "Night", "Mixed"])}
              </>
            )}

            {/* Growth & Puberty Markers */}
            {currentSection === 1 && (
              <>
                {renderQuestion("At what age did you notice signs of puberty beginning?", "pubertyAge", null, "text")}
                {renderQuestion("Have you had a recent growth spurt?", "recentGrowthSpurt", ["Yes", "No", "Not sure"])}
                {renderQuestion("How much have you grown in the past 12 months? (inches or cm)", "growthPast12Months", null, "text")}
                {renderQuestion("Do you experience growing pains in legs or knees?", "growingPains", ["Often", "Sometimes", "Rarely", "Never"])}
                {renderQuestion("Are your hands or feet still growing in size?", "handsFeetsGrowing", ["Yes", "No", "Not sure"])}
                {renderQuestion("Do you feel soreness or dull pain near knees, ankles, or wrists at night?", "nightSoreness", ["Yes", "No", "Sometimes"])}
              </>
            )}

            {/* Skeletal & Facial Development */}
            {currentSection === 2 && (
              <>
                {renderQuestion("Has your shoe size increased in the past year?", "shoeSizeIncrease", ["Yes", "No"])}
                {renderQuestion("Has your jawline or facial structure noticeably matured recently?", "facialMaturation", ["Yes", "No", "Slightly"])}
                {renderQuestion("Do you have noticeable Adam's apple growth?", "adamsApple", ["Yes", "No", "Not sure"])}
                {renderQuestion("Do you have facial hair?", "facialHair", ["None", "Light peach fuzz", "Full beard/mustache growth"])}
                {renderQuestion("Have your cheekbones become more prominent recently?", "cheekbones", ["Yes", "No", "Slightly"])}
                {renderQuestion("Has your voice changed in pitch or cracked more often recently?", "voiceChange", ["Yes", "No", "Sometimes"])}
                {renderQuestion("Has your overall body frame become wider (shoulders, chest)?", "bodyFrameWider", ["Yes", "No", "Slightly"])}
              </>
            )}

            {/* Testosterone/Hormone Traits */}
            {currentSection === 3 && (
              <>
                {renderQuestion("How would you describe your body hair?", "bodyHair", ["Minimal", "Moderate", "Dense"])}
                {renderQuestion("Do you gain muscle easily with workouts?", "muscleGain", ["Yes", "No", "Somewhat", "Unsure", "Never tried"])}
                {renderQuestion("Do you have acne or oily skin?", "acne", ["Mild", "Moderate", "Severe", "None"])}
                {renderQuestion("How deep is your voice?", "voiceDepth", ["High", "Medium", "Deep"])}
                {renderQuestion("Do you sweat more than you did 1–2 years ago?", "sweating", ["Yes", "No", "Not sure"])}
                {renderQuestion("Are your armpits or groin hair increasing rapidly?", "armpitGroinHair", ["Yes", "No", "Not sure"])}
                {renderQuestion("Have you noticed changes in jaw or nose definition in the past year?", "jawNoseDefinition", ["Yes", "No", "Slightly"])}
              </>
            )}

            {/* Genetics & Family Traits */}
            {currentSection === 4 && (
              <>
                {renderQuestion("What is your father's height?", "fatherHeight", null, "text")}
                {renderQuestion("What is your mother's height?", "motherHeight", null, "text")}
                {renderQuestion("Do you have siblings?", "hasSiblings", ["Yes", "No"])}
                {renderQuestion("If yes, what is their gender, age, height?", "siblingInfo", null, "textarea")}
                {renderQuestion("Did your parents hit puberty early, average, or late?", "parentsPublerty", ["Early", "Average", "Late", "Not sure"])}
                {renderQuestion("What ethnic background are you from (or mix of)?", "ethnicity", null, "text")}
                {renderQuestion("Has anyone in your family had late growth spurts (after age 17)?", "lateGrowthSpurts", ["Yes", "No", "Not sure"])}
              </>
            )}

            {/* Lifestyle & Health */}
            {currentSection === 5 && (
              <>
                {renderQuestion("How often do you exercise per week?", "exerciseFrequency", ["0", "1–2", "3–5", "6+ times"])}
                {renderQuestion("How many hours of sleep do you get per night?", "sleepHours", ["Less than 6", "6–8", "8+"])}
                {renderQuestion("Do you currently take any supplements or medications?", "supplements", ["Yes", "No"])}
                {renderQuestion("Do you drink milk or consume dairy regularly?", "dairy", ["Yes", "No"])}
                {renderQuestion("Do you eat a protein-rich diet?", "proteinDiet", ["Yes", "No", "Sometimes"])}
                {renderQuestion("Are you around cigarette smoke regularly?", "cigaretteSmoke", ["Yes", "No"])}
                {renderQuestion("How often do you eat soy sauce or soy-based foods?", "soyFoods", ["Often", "Sometimes", "Rarely", "Never"])}
                {renderQuestion("Are you currently experiencing a high-stress lifestyle?", "highStress", ["Yes", "No", "Sometimes"])}
                {renderQuestion("Do you have visible abs?", "visibleAbs", ["Yes", "No"])}
              </>
            )}

            {/* Psychological & Developmental Markers */}
            {currentSection === 6 && (
              <>
                {renderQuestion("Do you feel more mature or taller than most of your peers?", "maturityComparison", ["Yes", "No", "Same as others"])}
                {renderQuestion("Are you still noticeably growing taller month to month?", "stillGrowing", ["Yes", "No", "Not sure"])}
                {renderQuestion("Do you feel like your puberty is mostly finished, midway, or barely started?", "pubertyStage", ["Mostly finished", "Midway", "Barely started"])}
                {renderQuestion("Do you often compare your features to older siblings or parents?", "compareToFamily", ["Yes", "No", "Sometimes"])}
                {renderQuestion("Are your voice, feet, or facial structure more mature than your friends?", "maturityVsFriends", ["Yes", "No", "About the same"])}
              </>
            )}

            {/* Ethnicity Background */}
            {currentSection === 7 && (
              <>
                <div className="mb-6 p-4 bg-gray-800 rounded-lg">
                  <p className="text-sm text-gray-300">
                    <strong>Why does your ethnicity matter?</strong> Because your ethnic background plays a key role in predicting height.
                    Genetics determine how many growth spurts you'll have, how long you'll grow, when your growth plates will close, and more.
                    By understanding this, we can estimate your final height with top tier accuracy.
                  </p>
                </div>
                {renderQuestion("What country were you born in?", "birthCountry", null, "text")}
                {renderQuestion("What countries are your parents from?", "parentsCountries", null, "text")}
                {renderQuestion("Where were your grandparents born?", "grandparentsCountries", null, "text")}
                {renderQuestion("What ethnic group is your family part of? (For example: Chinese, Indian, Arab, African, European, etc.)", "ethnicGroup", null, "text")}
                {renderQuestion("Do you know if your family is mixed with more than one ethnicity?", "mixedEthnicity", ["Yes", "No", "Not sure"])}
              </>
            )}

            {/* Head and Skull Shape */}
            {currentSection === 8 && (
              <>
                {renderQuestion("Is your head more round, long, or somewhere in between?", "headShape", ["Round", "Long", "In between"])}
                {renderQuestion("Is the top of your head flat, rounded, or high?", "headTop", ["Flat", "Rounded", "High"])}
                {renderQuestion("Does the back of your head stick out a lot or is it flat?", "headBack", ["Sticks out a lot", "Flat", "Somewhere in between"])}
                {renderQuestion("Is your forehead straight up, curved back, or kind of slanted?", "forehead", ["Straight up", "Curved back", "Slanted"])}
                {renderQuestion("Is your head wider at the top or at the jaw?", "headWidth", ["Wider at top", "Wider at jaw", "About the same"])}
                {renderQuestion("Do you feel a raised line running down the top of your head?", "raisedLine", ["Yes", "No", "Not sure"])}
              </>
            )}

            {/* Face Shape and Features */}
            {currentSection === 9 && (
              <>
                {renderQuestion("Is your face more long and narrow, or short and wide?", "faceShape", ["Long and narrow", "Short and wide", "Somewhere in between"])}
                {renderQuestion("Do your cheekbones stick out, or are they flat?", "cheekbonesProminence", ["Stick out", "Flat", "Slightly prominent"])}
                {renderQuestion("Do you have a strong bone above your eyebrows (brow bone)?", "browBone", ["Yes, strong", "No, flat", "Slightly prominent"])}
                {renderQuestion("Is your nose flat and wide, medium, or tall and narrow?", "noseShape", ["Flat and wide", "Medium", "Tall and narrow"])}
                {renderQuestion("Are your nostrils round or more like slits?", "nostrils", ["Round", "Slit-like", "In between"])}
                {renderQuestion("Is there a lot of space between your eyes or just a little?", "eyeSpacing", ["A lot of space", "Little space", "Normal spacing"])}
                {renderQuestion("Is your chin pointy, round, or big and square?", "chinShape", ["Pointy", "Round", "Big and square"])}
                {renderQuestion("Is your jawline sharp, curved, or soft-looking?", "jawlineShape", ["Sharp", "Curved", "Soft-looking"])}
                {renderQuestion("Does your face stick out in the middle (like your nose area) or is it more flat?", "faceProfile", ["Sticks out", "Flat", "Slightly prominent"])}
                {renderQuestion("Is the area between your nose and upper lip long or short?", "noseLipSpace", ["Long", "Short", "Medium"])}
                {renderQuestion("Does your face grow more forward or downward?", "faceGrowthDirection", ["Forward", "Downward", "Both equally"])}
              </>
            )}

            {/* Eyes and Eyebrows */}
            {currentSection === 10 && (
              <>
                {renderQuestion("What color are your eyes naturally?", "eyeColor", ["Brown", "Blue", "Green", "Hazel", "Gray", "Other"])}
                {renderQuestion("Do you have an inner eyelid fold (like a monolid)?", "eyelidFold", ["Yes", "No", "Partially"])}
                {renderQuestion("Are your eyes deep-set or do they stick out more?", "eyeDepth", ["Deep-set", "Stick out", "Normal"])}
                {renderQuestion("Do you have double eyelids, single eyelids, or something in between?", "eyelidType", ["Double eyelids", "Single eyelids", "In between"])}
                {renderQuestion("Are your eyebrows straight, arched, or angled?", "eyebrowShape", ["Straight", "Arched", "Angled"])}
                {renderQuestion("How much space is there between your eyebrows and your eyes?", "eyebrowEyeSpace", ["A lot of space", "Little space", "Normal spacing"])}
              </>
            )}

            {/* Final Consent */}
            {currentSection === 11 && (
              <>
                <div className="mb-8 p-6 bg-gradient-to-r from-[#00C2A8]/10 to-[#45B7D1]/10 rounded-lg border border-[#00C2A8]/20">
                  <h3 className="text-xl font-bold text-white mb-4">🎉 Almost Done!</h3>
                  <p className="text-gray-300 mb-4">
                    You're about to receive your personalized genetic height analysis report.
                    This comprehensive report will include your growth potential, genetic markers,
                    and personalized recommendations based on your unique profile.
                  </p>
                </div>

                {renderQuestion("What is your Gmail address to receive your results?", "email", null, "text")}

                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-white mb-4">Final Consent</h3>
                  <div className="p-4 bg-gray-800 rounded-lg mb-4">
                    <p className="text-sm text-gray-300">
                      By proceeding, you agree to receive your personalized genetic analysis report and insights.
                      Your data will be processed securely and used only for generating your personalized report.
                    </p>
                  </div>
                  {renderQuestion("Do you agree to receive your personalized report and insights?", "consent", null, "checkbox")}
                </div>
              </>
            )}
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-12 pt-8 border-t border-gray-600">
            <button
              onClick={prevSection}
              disabled={currentSection === 0}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500 transition"
            >
              Previous
            </button>
            
            {currentSection === sections.length - 1 ? (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || !quizData.email || !quizData.consent}
                className="cta-button px-8 py-3 rounded-lg font-bold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Processing...' : 'Reveal Your Blueprint – $1'}
              </button>
            ) : (
              <button
                onClick={nextSection}
                className="cta-button px-6 py-3 rounded-lg font-semibold"
              >
                Next
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Quiz;
