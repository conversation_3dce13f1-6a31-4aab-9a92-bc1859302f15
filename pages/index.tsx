import Head from 'next/head';
import { useState } from 'react';

export default function Home() {
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const faqs = [
    {
      question: 'Is this accurate without a DNA test?',
      answer: 'Yes — we trained on thousands of data points. Our accuracy is >95%.'
    },
    {
      question: 'Do I need a doctor?',
      answer: 'Nope. You just answer questions, our systems deliver.'
    },
    {
      question: 'Can this help with looksmaxing?',
      answer: 'Yes. It helps you know if your growth plates are open, how much potential you have left, and what areas to improve.'
    }
  ];

  return (
    <div className="bg-[#141735] min-h-screen flex flex-col">
      <Head>
        <title>BioAscension – Discover Your Genetic Potential</title>
        <meta name="description" content="Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of." />
      </Head>
      {/* Navbar */}
      <header className="sticky top-0 z-30 bg-[#141735] text-white shadow flex items-center justify-between px-8 py-4">
        <div className="font-extrabold text-2xl tracking-tight flex items-center gap-2">
          <span className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-transparent bg-clip-text">BioAscension</span>
        </div>
        <nav className="hidden md:flex gap-10 text-base font-semibold mx-auto">
          <a href="#how" className="hover:text-teal-400 transition">How It Works</a>
          <a href="#pricing" className="hover:text-teal-400 transition">Pricing</a>
          <a href="#faq" className="hover:text-teal-400 transition">FAQ</a>
          <a href="#contact" className="hover:text-teal-400 transition">Contact</a>
        </nav>
        <a href="#quiz" className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-6 py-2 rounded-lg shadow font-bold hover:from-teal-500 hover:to-blue-900 transition">Start Quiz</a>
      </header>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden hero-gradient-bg">

        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-6 md:px-12 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

            {/* Left Content */}
            <div className="text-center lg:text-left">
              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-[#00C2A8]/20 to-[#45B7D1]/20 border border-[#00C2A8]/30 mb-8">
                <span className="text-[#00C2A8] text-sm font-semibold">🚀 World's First AI-Powered Genetic Analysis</span>
              </div>

              {/* Main Headline */}
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-extrabold mb-6 leading-tight">
                <span className="bg-gradient-to-r from-white via-[#00C2A8] to-[#45B7D1] text-transparent bg-clip-text">
                  Discover Your
                </span>
                <br />
                <span className="text-white">Genetic Potential</span>
              </h1>

              {/* Subtitle */}
              <p className="text-xl md:text-2xl mb-8 text-gray-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Unlock the secrets of your DNA. Optimize your face, height, and frame by understanding what your
                <span className="text-[#00C2A8] font-semibold"> genetics and puberty </span>
                are truly capable of achieving.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                <a
                  href="#quiz"
                  className="cta-button text-white px-10 py-4 rounded-2xl text-xl font-bold inline-flex items-center justify-center gap-3 group"
                >
                  <span>Start Your Analysis</span>
                  <svg className="w-6 h-6 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </a>

                <a
                  href="#how"
                  className="bg-transparent border-2 border-[#00C2A8] text-[#00C2A8] px-10 py-4 rounded-2xl text-xl font-bold hover:bg-[#00C2A8] hover:text-white transition-all duration-300 inline-flex items-center justify-center gap-3"
                >
                  <span>Learn More</span>
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </a>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap items-center justify-center lg:justify-start gap-8 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>Science-Based Analysis</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <span>Instant Results</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                  <span>95% Accuracy Rate</span>
                </div>
              </div>
            </div>

            {/* Right Visual */}
            <div className="relative flex items-center justify-center">
              {/* Main Visual Container */}
              <div className="relative w-96 h-96 lg:w-[500px] lg:h-[500px]">
                {/* Animated Background Rings */}
                <div className="absolute inset-0 rounded-full border border-[#00C2A8]/20 animate-pulse"></div>
                <div className="absolute inset-4 rounded-full border border-[#45B7D1]/30 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                <div className="absolute inset-8 rounded-full border border-[#4ECDC4]/40 animate-pulse" style={{ animationDelay: '1s' }}></div>

                {/* Central DNA Helix */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative w-64 h-64 lg:w-80 lg:h-80 professional-card rounded-full flex items-center justify-center glow-effect">
                    <div className="text-8xl lg:text-9xl floating-dna">🧬</div>
                  </div>
                </div>

                {/* Floating Data Points */}
                <div className="absolute top-12 right-12 professional-card rounded-2xl p-4 animate-bounce">
                  <div className="text-2xl font-bold text-[#00C2A8]">95%</div>
                  <div className="text-xs text-gray-300">Accuracy</div>
                </div>

                <div className="absolute bottom-12 left-12 professional-card rounded-2xl p-4 animate-bounce" style={{ animationDelay: '1s' }}>
                  <div className="text-2xl font-bold text-[#45B7D1]">10K+</div>
                  <div className="text-xs text-gray-300">Analyzed</div>
                </div>

                <div className="absolute top-1/2 left-0 professional-card rounded-2xl p-4 animate-bounce" style={{ animationDelay: '2s' }}>
                  <div className="text-2xl font-bold text-[#4ECDC4]">AI</div>
                  <div className="text-xs text-gray-300">Powered</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Gradient Fade */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#141735] to-transparent"></div>
      </section>

      {/* Why BioAscension */}
      <section className="py-20 px-6 md:px-20" id="why" style={{ backgroundColor: '#141735' }}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#00C2A8' }}>Why Choose BioAscension?</h2>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto">Advanced genetic analysis powered by cutting-edge AI and validated by thousands of data points</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Instant Results Card */}
            <div className="group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Instant Results</h3>
              <p className="text-gray-600 mb-4">Get comprehensive genetic analysis in seconds, not weeks</p>
              <div className="flex items-center text-sm text-[#00C2A8] font-semibold">
                <span className="w-2 h-2 bg-[#00C2A8] rounded-full mr-2"></span>
                No lab visits required
              </div>
            </div>

            {/* 95% Accuracy Card */}
            <div className="group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">95% Accuracy</h3>
              <p className="text-gray-600 mb-4">Validated against thousands of real genetic profiles and outcomes</p>
              <div className="flex items-center text-sm text-[#00C2A8] font-semibold">
                <span className="w-2 h-2 bg-[#00C2A8] rounded-full mr-2"></span>
                Science-backed predictions
              </div>
            </div>

            {/* Affordable Card */}
            <div className="group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Affordable Access</h3>
              <p className="text-gray-600 mb-4">Professional genetic analysis starting from just $1.00</p>
              <div className="flex items-center text-sm text-[#00C2A8] font-semibold">
                <span className="w-2 h-2 bg-[#00C2A8] rounded-full mr-2"></span>
                No hidden fees
              </div>
            </div>

            {/* Optimization Tool Card */}
            <div className="group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]"></div>
              <div className="w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Optimization Tool</h3>
              <p className="text-gray-600 mb-4">Maximize your genetic potential with personalized recommendations</p>
              <div className="flex items-center text-sm text-[#00C2A8] font-semibold">
                <span className="w-2 h-2 bg-[#00C2A8] rounded-full mr-2"></span>
                Tailored for growth phases
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-white text-center">
              <div>
                <div className="text-3xl font-bold mb-2">10,000+</div>
                <div className="text-sm opacity-90">Genetic Profiles Analyzed</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">95%</div>
                <div className="text-sm opacity-90">Prediction Accuracy</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">24/7</div>
                <div className="text-sm opacity-90">Expert Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 px-6 md:px-20" id="how" style={{ backgroundColor: '#141735' }}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#00C2A8' }}>How It Works</h2>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto">Our advanced AI analyzes your genetic markers through a simple questionnaire to deliver precise predictions</p>
          </div>

          {/* Process Flow */}
          <div className="relative">
            {/* Connection Lines */}
            <div className="hidden md:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-[#00C2A8] via-[#00C2A8] to-[#0A0E3F] transform -translate-y-1/2 z-0"></div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-12 relative z-10">
              {/* Step 1: Take the Quiz */}
              <div className="group flex flex-col items-center text-center">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-sm font-bold text-[#0A0E3F]">1</span>
                  </div>
                </div>

                <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-sm group-hover:shadow-2xl transition-shadow duration-300">
                  <h3 className="text-xl font-bold mb-4 text-gray-800">Complete Assessment</h3>
                  <p className="text-gray-600 mb-6">Answer detailed questions about your growth patterns, family history, and physical development</p>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>10-15 minutes</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>50+ data points</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>Privacy protected</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 2: AI Analysis */}
              <div className="group flex flex-col items-center text-center">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-sm font-bold text-[#0A0E3F]">2</span>
                  </div>
                </div>

                <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-sm group-hover:shadow-2xl transition-shadow duration-300">
                  <h3 className="text-xl font-bold mb-4 text-gray-800">AI Analysis</h3>
                  <p className="text-gray-600 mb-6">Our advanced algorithms process your data against thousands of genetic profiles and growth patterns</p>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>Machine learning models</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>10,000+ data points</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>Real-time processing</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 3: Get Results */}
              <div className="group flex flex-col items-center text-center">
                <div className="relative mb-8">
                  <div className="w-24 h-24 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-sm font-bold text-[#0A0E3F]">3</span>
                  </div>
                </div>

                <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-sm group-hover:shadow-2xl transition-shadow duration-300">
                  <h3 className="text-xl font-bold mb-4 text-gray-800">Instant Results</h3>
                  <p className="text-gray-600 mb-6">Receive comprehensive genetic potential report with actionable insights and recommendations</p>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>Detailed PDF report</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>Personalized recommendations</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-700">
                      <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3"></div>
                      <span>Email delivery</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Technology Stack */}
          <div className="mt-20 bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-white text-center mb-8">Powered by Advanced Technology</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-white text-center">
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                  </svg>
                </div>
                <span className="text-sm font-semibold">Machine Learning</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                  </svg>
                </div>
                <span className="text-sm font-semibold">Big Data Analytics</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                  </svg>
                </div>
                <span className="text-sm font-semibold">Secure Processing</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <span className="text-sm font-semibold">Validated Results</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sample Predictions Dashboard */}
      <section className="py-16 px-6 md:px-20" id="dashboard" style={{ backgroundColor: '#141735' }}>
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-4" style={{ color: '#00C2A8' }}>See Your Potential</h2>
        <p className="text-center text-gray-300 mb-12 max-w-2xl mx-auto">Get detailed insights into your genetic potential with our advanced prediction models</p>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12">
          {/* Height Growth Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">📏</div>
              <h3 className="font-bold mb-3 text-center">Height Growth Range</h3>

              {/* Enhanced Progress Bar with Animation */}
              <div className="w-full mb-4">
                <div className="flex justify-between text-xs mb-1">
                  <span>5'8"</span>
                  <span>6'2"</span>
                </div>
                <div className="w-full h-3 bg-white bg-opacity-20 rounded-full overflow-hidden">
                  <div className="h-3 bg-gradient-to-r from-white to-teal-200 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-sm font-semibold">Current: 5'10"</span>
                </div>
              </div>

              {/* Growth Chart Visualization */}
              <div className="w-full h-12 flex items-end justify-center space-x-1">
                <div className="w-2 h-4 bg-white bg-opacity-60 rounded-t"></div>
                <div className="w-2 h-6 bg-white bg-opacity-70 rounded-t"></div>
                <div className="w-2 h-8 bg-white bg-opacity-80 rounded-t"></div>
                <div className="w-2 h-10 bg-white rounded-t"></div>
                <div className="w-2 h-12 bg-teal-200 rounded-t animate-bounce"></div>
              </div>
            </div>
          </div>

          {/* Testosterone Trajectory Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute top-0 left-0 w-16 h-16 bg-white bg-opacity-10 rounded-full -ml-8 -mt-8"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">🧪</div>
              <h3 className="font-bold mb-3 text-center">Testosterone Trajectory</h3>

              {/* Enhanced Bar Chart */}
              <div className="w-full h-20 flex items-end justify-center space-x-2 mb-3">
                <div className="flex flex-col items-center">
                  <div className="w-3 h-8 bg-white bg-opacity-60 rounded-t mb-1"></div>
                  <span className="text-xs">16</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-12 bg-white bg-opacity-70 rounded-t mb-1"></div>
                  <span className="text-xs">17</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-16 bg-white bg-opacity-80 rounded-t mb-1"></div>
                  <span className="text-xs">18</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-14 bg-white bg-opacity-90 rounded-t mb-1"></div>
                  <span className="text-xs">19</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-20 bg-teal-200 rounded-t mb-1 animate-pulse"></div>
                  <span className="text-xs font-bold">20</span>
                </div>
              </div>

              <div className="text-center">
                <span className="text-sm font-semibold bg-white bg-opacity-20 px-2 py-1 rounded">📈 Rising Trend</span>
              </div>
            </div>
          </div>

          {/* Facial Bone Maturity Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -mr-12 -mb-12"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">💀</div>
              <h3 className="font-bold mb-3 text-center">Facial Bone Maturity</h3>

              {/* Circular Progress */}
              <div className="relative w-20 h-20 mb-3">
                <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-white text-opacity-20"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-teal-200"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeDasharray="50, 100"
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold">50%</span>
                </div>
              </div>

              <div className="text-center">
                <span className="text-sm font-semibold">Still Growing</span>
                <div className="text-xs mt-1 bg-white bg-opacity-20 px-2 py-1 rounded">Peak at 25</div>
              </div>
            </div>
          </div>

          {/* Puberty Stage Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute top-0 right-0 w-32 h-8 bg-white bg-opacity-10 rounded-full -mr-16 -mt-4 transform rotate-45"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">⏳</div>
              <h3 className="font-bold mb-3 text-center">Puberty Stage</h3>

              {/* Stage Indicator */}
              <div className="w-full mb-4">
                <div className="flex justify-between text-xs mb-2">
                  <span>Early</span>
                  <span>Peak</span>
                  <span>Late</span>
                </div>
                <div className="w-full h-3 bg-white bg-opacity-20 rounded-full overflow-hidden">
                  <div className="h-3 bg-gradient-to-r from-white to-teal-200 rounded-full" style={{ width: '80%' }}></div>
                </div>
              </div>

              {/* Timeline Dots */}
              <div className="flex justify-between w-full mb-3">
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <div className="w-3 h-3 bg-teal-200 rounded-full animate-ping"></div>
                <div className="w-3 h-3 bg-white bg-opacity-40 rounded-full"></div>
              </div>

              <div className="text-center">
                <span className="text-sm font-semibold">Late Stage</span>
                <div className="text-xs mt-1">80% Complete</div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Visual Elements */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8 text-white text-center">
            <h3 className="text-xl font-bold mb-4">🎯 Your Genetic Potential Score</h3>
            <div className="flex justify-center items-center space-x-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-200">8.7</div>
                <div className="text-sm">Overall Score</div>
              </div>
              <div className="text-6xl opacity-20">|</div>
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-200">95%</div>
                <div className="text-sm">Accuracy</div>
              </div>
              <div className="text-6xl opacity-20">|</div>
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-200">2.3</div>
                <div className="text-sm">Years Left</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 px-6 md:px-20" id="pricing" style={{ backgroundColor: '#141735' }}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#00C2A8' }}>Pricing</h2>
            <p className="text-lg text-gray-300 max-w-3xl mx-auto">Choose the plan that fits your needs. No hidden fees, no subscriptions, just transparent pricing.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Basic Plan */}
            <div className="group bg-white rounded-2xl shadow-2xl p-8 flex flex-col relative overflow-hidden hover:shadow-3xl transition-all duration-300 hover:-translate-y-2">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]"></div>

              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold mb-2 text-gray-800">Essential Report</h3>
                <div className="flex items-center justify-center mb-4">
                  <span className="text-4xl font-bold text-[#00C2A8]">$1.00</span>
                  <span className="text-gray-500 ml-2">one-time</span>
                </div>
                <p className="text-gray-600">Perfect for getting started with your genetic analysis</p>
              </div>

              <div className="flex-1 mb-8">
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Instant growth & puberty analysis</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Basic genetic potential assessment</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">PDF report delivered to email</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Basic recommendations</span>
                  </li>
                </ul>
              </div>

              <button className="w-full bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white py-4 rounded-xl font-bold text-lg hover:from-teal-500 hover:to-blue-900 transition-all duration-300 shadow-lg hover:shadow-xl">
                Get Started
              </button>
            </div>

            {/* Premium Plan */}
            <div className="group bg-white rounded-2xl shadow-2xl p-8 flex flex-col relative overflow-hidden hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 border-2 border-[#00C2A8]">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]"></div>
              <div className="absolute -top-4 right-6 bg-[#00C2A8] text-white px-4 py-2 rounded-full text-sm font-bold">
                MOST POPULAR
              </div>

              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold mb-2 text-gray-800">Maximizing Potential Blueprint</h3>
                <div className="flex items-center justify-center mb-4">
                  <span className="text-4xl font-bold text-[#0A0E3F]">$4.99</span>
                  <span className="text-gray-500 ml-2">one-time</span>
                </div>
                <p className="text-gray-600">Complete optimization guide for maximum results</p>
              </div>

              <div className="flex-1 mb-8">
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Everything in Essential Report</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Advanced looksmax optimization tips</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Detailed puberty phase timeline</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Projected final height & features</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Personalized action plan</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Priority email support</span>
                  </li>
                </ul>
              </div>

              <button className="w-full bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] text-white py-4 rounded-xl font-bold text-lg hover:from-blue-900 hover:to-teal-500 transition-all duration-300 shadow-lg hover:shadow-xl">
                Get Complete Analysis
              </button>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 text-center">
            <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-12 mb-8">
              <div className="flex items-center text-gray-300">
                <svg className="w-6 h-6 text-[#00C2A8] mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                <span>Secure Payment Processing</span>
              </div>
              <div className="flex items-center text-gray-300">
                <svg className="w-6 h-6 text-[#00C2A8] mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>No Hidden Fees</span>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Secure payments powered by</p>
                <div className="bg-white rounded-lg px-6 py-3 shadow-sm border border-gray-200 inline-flex items-center space-x-4">
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-[#6772E5] tracking-tight">stripe</span>
                  </div>
                  <div className="h-6 w-px bg-gray-300"></div>
                  <div className="flex items-center space-x-4 text-xs text-gray-600">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                      <span>SSL Encrypted</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-blue-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span>PCI Compliant</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 px-6 md:px-20" id="faq" style={{ backgroundColor: '#141735' }}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#00C2A8' }}>Frequently Asked Questions</h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">Get answers to common questions about our genetic analysis platform</p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, idx) => (
              <div key={idx} className="group bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300">
                <button
                  className="w-full text-left p-8 focus:outline-none flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  onClick={() => setOpenFaq(openFaq === idx ? null : idx)}
                  aria-expanded={openFaq === idx}
                  aria-controls={`faq-answer-${idx}`}
                >
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="text-xl font-bold text-gray-800">{faq.question}</span>
                  </div>
                  <div className={`ml-4 transition-all duration-300 ${openFaq === idx ? 'rotate-180 text-[#00C2A8]' : 'text-gray-400'}`}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>
                {openFaq === idx && (
                  <div id={`faq-answer-${idx}`} className="px-8 pb-8 animate-fade-in">
                    <div className="pl-16 pr-4">
                      <div className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] h-0.5 w-full mb-4 opacity-30"></div>
                      <p className="text-gray-700 text-lg leading-relaxed">{faq.answer}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Need Help Section */}
          <div className="mt-16 bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8 text-center text-white">
            <h3 className="text-2xl font-bold mb-4">Need Help?</h3>
            <p className="text-lg mb-6 opacity-90">Our team of genetic analysis experts is here to help you understand your results</p>
            <p className="text-lg mb-4">Email support: <a href="mailto:<EMAIL>" className="text-white underline hover:text-gray-200 transition-colors duration-300 font-semibold"><EMAIL></a></p>
            <div className="text-sm opacity-75">
              Average response time: 2 hours
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="bg-[#0A0E3F] text-white py-8 px-6 md:px-20 mt-auto">
        <div className="flex flex-col md:flex-row justify-between items-center max-w-6xl mx-auto">
          <div className="font-bold text-lg mb-4 md:mb-0">BioAscension</div>
          <div className="flex gap-6 text-sm mb-4 md:mb-0">
            <a href="#" className="hover:text-teal-400">Home</a>
            <a href="#how" className="hover:text-teal-400">How It Works</a>
            <a href="#pricing" className="hover:text-teal-400">Pricing</a>
            <a href="#faq" className="hover:text-teal-400">FAQ</a>
            <a href="#contact" className="hover:text-teal-400">Contact</a>
          </div>
          <div className="text-xs text-gray-300">© 2024 BioAscension. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
}
